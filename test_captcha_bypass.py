from bypass_captcha import recaptcha1


def random_user_agent():
    """Generate a user agent."""
    return "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"

url = "https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LfcCh8pAAAAAN5MgtopKxl-4Gon3-MKg2RPqmXI&co=aHR0cHM6Ly9zYWthbmkuc2E6NDQz&hl=ar&v=Lu6n5xwy2ghvnPNo3IxkhcCb&size=invisible&anchor-ms=20000&execute-ms=15000&cb=7zfyjqjemyzq"

cap = recaptcha1(url, False, random_user_agent())

async def main():
    print(await cap)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())