import http.client
import gzip
from urllib.parse import urlparse
import json
import subprocess
import os
from sakani_updater import SakaniUpdaterThread

updater = SakaniUpdaterThread()
updater.start()

auth = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************.R0ATy8phAUJBY17Fin5Rj_AAsmIOgnI6gIQ9NGDT5TLbM1ZdWTll_yFCEjBt1tu00igbgHlQVNy9TqPEId2g7vPfCbCVDeuKAJlFWnw-dudBH2bdxmphJ8soa75MeT3qzCoNeGgvQmvDFClwmG2EI3iRUzv3qKN1rc0kjISwQYvZr11A-q7twnhifrb3LSvmZcu4y8Ir4wvvSNukGYfAseom89sdnBKzxXZBukINxIdafyYjRw_kUZSA4zk_y-rk8etakquHE8RxBgmFExs26bLywMCdMsBE9JdWDHG8rHjUaGk9XzIcvy4sMpAM58KgnEASonCYDXhxi6mppnCoFg"
class HttpClientResponseWrapper:
    """A wrapper to make http.client response behave like requests.Response."""

    def __init__(self, status: int, body: bytes, headers: http.client.HTTPMessage):
        self.status_code = status
        self._body = body
        self.headers = headers
        self.text = None
        try:
            self.text = body.decode('utf-8')
        except UnicodeDecodeError:
            pass

    def json(self):
        """Parses the response body as JSON."""
        if self.text is None:
            raise json.JSONDecodeError(
                "Cannot decode JSON from an empty or non-UTF-8 body.", "", 0)
        return json.loads(self.text)

main_headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    'Sec-GPC': '1',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Priority': 'u=0, i',
    'TE': 'trailers',
}
timeout = 5
def _make_request(method: str, url: str, extra_headers: dict = None, json_payload: dict = None) -> HttpClientResponseWrapper:
    parsed_url = urlparse(url)
    host = parsed_url.netloc
    path = parsed_url.path
    if parsed_url.query:
        path += "?" + parsed_url.query
    
    final_headers = main_headers.copy()
    if extra_headers:
        final_headers.update(extra_headers)
    final_headers.update(updater.cookie_header)
    
    conn = http.client.HTTPSConnection(host, timeout=timeout)

    body = None
    if json_payload is not None:
        body = json.dumps(json_payload).encode('utf-8')
        final_headers['Content-Type'] = 'application/json'
        final_headers['Content-Length'] = str(len(body))
    final_headers['authentication'] = auth.replace(" ", "")
    try:
        conn.request(method, path, body=body, headers=final_headers)
        res = conn.getresponse()
        response_body = res.read()

        if res.getheader('Content-Encoding') == 'gzip':
            response_body = gzip.decompress(response_body)

        return HttpClientResponseWrapper(res.status, response_body, res.getheaders())
    finally:
        conn.close()

def solve_captcha_with_exe(captcha_url: str, user_agent: str) -> str | None:
    """
    Runs the solver.exe to get a reCAPTCHA token.
    """
    exe_path = "solver_cli.exe" 
    
    if not os.path.exists(exe_path):
        print(f"Error: Executable not found at '{exe_path}'")
        return None
    command = [exe_path, captcha_url, user_agent]
    print(f"Running command: {' '.join(command)}")
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True
            )
        token = result.stdout.strip()
        token = token.split('**********\n')[1]
        print("Successfully captured token!")
        return token
    except Exception as e:
        print(f"Error solving captcha: {e}")
        return None

def get_national_id():
    headers = {
        'accept': 'application/json',
        'accept-language': 'ar',
        'app-locale': 'ar',
        'content-type': 'application/json',
        'if-none-match': 'W/"af2958fada5bad09fcd628466e2c15d5"',
        'priority': 'u=1, i',
        'referer': 'https://sakani.sa/app/booking/offplan/disclaimer',
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    response = _make_request('GET', 'https://sakani.sa/mainIntermediaryApi/v4/beneficiary/me?include=beneficiary_assets_detail,beneficiary_application,active_booking,active_subsidy_contract,active_booking_session', extra_headers=headers)
    data = response.json()
    national_id = data['data']['attributes']['idnational_id_number']
    return national_id

def validate_request(national_id):
    headers = {
        'accept': 'application/json',
        'accept-language': 'ar',
        'app-locale': 'ar',
        'content-type': 'application/json',
        'origin': 'https://sakani.sa',
        'priority': 'u=1, i',
        'referer': 'https://sakani.sa/app/units/1042379',
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    # solve captcha
    
    captcha_url = "https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LfcCh8pAAAAAN5MgtopKxl-4Gon3-MKg2RPqmXI&co=aHR0cHM6Ly9zYWthbmkuc2E6NDQz&hl=ar&v=Lu6n5xwy2ghvnPNo3IxkhcCb&size=invisible&anchor-ms=20000&execute-ms=15000&cb=7zfyjqjemyzq"
    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    recaptcha_challenge = solve_captcha_with_exe(captcha_url, user_agent)
    json_data = {
        'data': {
            'attributes': {
                'action': 'start_booking',
                'identifier': national_id,
                'recaptcha_challenge': recaptcha_challenge,
                'recaptcha_type': 'grecaptchaV3',
                'payload': {
                    'data': {
                        'attributes': {
                            'unit_code': '0BB3D53060D865D5079971B3246A9E05194D01D423F1A7033D4DB2B576457830',
                            'project_id': 164,
                        },
                    },
                },
                'site_key': '6LfcCh8pAAAAAN5MgtopKxl-4Gon3-MKg2RPqmXI',
            },
        },
    }

    response = _make_request('POST', 'https://sakani.sa/captchaApi/grecaptcha/validate', extra_headers=headers, json_payload=json_data)
    data = response.json()
    return data
def start_booking():
    headers = {
    'accept': 'application/json',
    'accept-language': 'ar',
    'app-locale': 'ar',
    'content-type': 'application/json',
    'origin': 'https://sakani.sa',
    'priority': 'u=1, i',
    'referer': 'https://sakani.sa/app/units/1042379',
    'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    json_data = {
        'project_id': 164,
        'source_channel': 'web',
    }
    response = _make_request('POST', 'https://sakani.sa/mainIntermediaryApi/v4/bookings/offplans/1493/start_booking', extra_headers=headers, json_payload=json_data)
    data = response.json()
    return data
def start_booking_cqrs_check():
    
    headers = {
        'accept': 'application/json',
        'accept-language': 'ar',
        'app-locale': 'ar',
        'content-type': 'application/json',
        'priority': 'u=1, i',
        'referer': 'https://sakani.sa/app/units/1042379',
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    params = {
        'topic': 'booking_precondition_and_generate_token_check_completed',
        'request_id': 'ef670df3643536bb4ed4fc66',
    }

    response = _make_request('GET', 'https://sakani.sa/sakani-queries-service/cqrs-res', extra_headers=headers, params=params)
    data: dict = response.json()
    if not data.get(list(data.keys())[0], None) == "success":
        print("Waiting")
    else:
        return data
def reserve(recaptcha_auth_token: str, national_id: str):
    headers = {
        'accept': 'application/json',
        'accept-language': 'ar',
        'app-locale': 'ar',
        'content-type': 'application/json',
        'origin': 'https://sakani.sa',
        'priority': 'u=1, i',
        'referer': 'https://sakani.sa/app/units/1042379',
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    json_data = {
        'data': {
            'id': '',
            'type': 'units',
            'attributes': {
                'unit_code': '0BB3D53060D865D5079971B3246A9E05194D01D423F1A7033D4DB2B576457830',
                'national_id_number': national_id,
            },
        },
        'recaptcha_auth_token': recaptcha_auth_token,
    }

    response = _make_request('POST', 'https://sakani.sa/mainIntermediaryApi/v4/units/reserve', extra_headers=headers, json_payload=json_data)
    data = response.json()
    return data


def main():
    """Main function to run the test"""
    print("🧪 Standalone Sakani Reservation Tester")
    print("=" * 40)
    national_id = get_national_id()
    print(f"National ID: {national_id}")
    print("=" * 40)
    data = validate_request(national_id)
    print(data)
    recaptcha_auth_token = data['data']['attributes']['recaptcha_auth_token']
    print(recaptcha_auth_token)
    start_booking_data = start_booking()
    start_booking_request_id = start_booking_data['data']['request_id']
    print(start_booking_request_id)
    start_booking_cqrs_check_data = start_booking_cqrs_check()
    print(start_booking_cqrs_check_data)
    reserve_data = reserve(recaptcha_auth_token, national_id)
    print(reserve_data)
    


if __name__ == "__main__":
    main()
    

