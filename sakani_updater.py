import nodriver
import asyncio
import logging
import threading
import time
from dataclasses import dataclass, field
from enum import Enum, auto
from typing import List, Dict, Any, Optional

# 1. Structured Logging Setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logging.getLogger('uc').setLevel(logging.WARNING)
log = logging.getLogger(__name__)


# 2. Configuration Management
@dataclass
class Config:
    """Holds all configuration for the Sakani Updater."""
    TARGET_URL: str = 'https://sakani.sa/mainIntermediaryApi/v4/beneficiary/me'
    SUCCESS_INTERVAL: int = 900  # 15 minutes
    INITIAL_BACKOFF_INTERVAL: int = 60  # 1 minute
    MAX_BACKOFF_INTERVAL: int = 900  # 15 minutes
    BROWSER_TIMEOUT: int = 60  # 1 minute for page loads
    HEADLESS: bool = True


# 3. State Management Enums
class Status(Enum):
    """Defines the possible states of the updater."""
    IDLE = auto()
    RUNNING = auto()
    STOPPING = auto()
    STOPPED = auto()
    ERROR = auto()


# 4. Core Asynchronous Updater Class
class SakaniUpdater:
    """
    (AsyncIO-based) Manages fetching Sakani cookies in the background.
    This class is intended to be run within an asyncio event loop.
    """

    def __init__(self, config: Optional[Config] = None):
        self.config =  Config()
        self.status = Status.IDLE
        self.last_update_time: Optional[float] = None
        self.last_error_time: Optional[float] = None
        self.last_error_message: Optional[str] = None
        self.success_count: int = 0
        self.failure_count: int = 0
        self.latest_cookies: Optional[List[Dict[str, Any]]] = None
        self._task: Optional[asyncio.Task] = None
        self._current_backoff: int = self.config.INITIAL_BACKOFF_INTERVAL

    @property
    def cookie_header(self) -> Dict[str, str]:
        """Formats the latest cookies into a dict for use in request headers."""
        if not self.latest_cookies:
            return {}
        # Format cookies as a single Cookie header string
        cookie_pairs = [f"{c.name}={c.value}" for c in self.latest_cookies]
        return {"Cookie": "; ".join(cookie_pairs)}

    async def _get_sakani_cookie(self):
        log.info("Attempting to launch browser...")
        browser = None
        try:
            browser = await nodriver.start(headless=self.config.HEADLESS)
            log.info("Browser started. Navigating to target URL.")
            page = await browser.get(
                self.config.TARGET_URL,
            )
            log.info("Page is ready. Attempting to retrieve cookies.")

            # --- New: Get all cookies ---
            all_cookies = await browser.cookies.get_all()
            if all_cookies:
                self.latest_cookies = all_cookies
                log.info(f"Successfully retrieved {len(all_cookies)} cookies.")
            else:
                log.warning("Could not retrieve any cookies.")
            # --- End New ---

        except Exception as e:
            log.error(f"An error occurred during browser operation: {type(e).__name__} - {e}")
            raise
        finally:
            if browser:
                try:
                    await browser.stop()
                except:
                    pass
            log.info("Browser closed.")

    async def _updater_loop(self):
        log.info("Updater loop started.")
        self.status = Status.RUNNING
        while self.status == Status.RUNNING:
            try:
                await self._get_sakani_cookie()
                self.success_count += 1
                self.last_update_time = time.time()
                log.info(f"Update successful. Total successes: {self.success_count}.")
                self._current_backoff = self.config.INITIAL_BACKOFF_INTERVAL
                wait_interval = self.config.SUCCESS_INTERVAL
            except Exception as e:
                self.failure_count += 1
                self.last_error_time = time.time()
                self.last_error_message = str(e)
                log.warning(f"Update failed. Total failures: {self.failure_count}.")
                wait_interval = self._current_backoff
                self._current_backoff = min(
                    self._current_backoff * 2, self.config.MAX_BACKOFF_INTERVAL
                )
            log.info(f"Next update in {wait_interval} seconds.")
            await asyncio.sleep(wait_interval)
        log.info("Updater loop has exited.")

    def start_task(self, loop: asyncio.AbstractEventLoop):
        """Creates and schedules the background task on the provided event loop."""
        if self.status in [Status.IDLE, Status.STOPPED]:
            self.status = Status.RUNNING
            self._task = loop.create_task(self._updater_loop())

    async def stop_task(self):
        """Stops the background task."""
        if self.status != Status.RUNNING:
            return
        self.status = Status.STOPPING
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass  # Expected on cancellation
        self.status = Status.STOPPED
        log.info("Updater task stopped.")


# 5. Synchronous Thread-Safe Wrapper
class SakaniUpdaterThread:
    """
    A thread-safe wrapper to run the SakaniUpdater from synchronous code.
    The start() method blocks until the initial cookie retrieval is complete.
    """

    def __init__(self, config: Optional[Config] = None):
        self._updater = SakaniUpdater(config or Config())
        self._thread: Optional[threading.Thread] = None
        self._loop: Optional[asyncio.AbstractEventLoop] = None
        # New: Event to signal when the first run is complete
        self._initial_run_event = threading.Event()

    def _run_background_loop(self):
        """
        Target for the background thread.
        Performs an initial run, signals completion, then starts the main loop.
        """
        try:
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)

            # --- Step 1: Perform the initial, blocking update ---
            log.info("Performing initial cookie retrieval...")
            try:
                # This now correctly runs the task until it's finished.
                self._loop.run_until_complete(self._updater._get_sakani_cookie())
                self._updater.success_count += 1
                self._updater.last_update_time = time.time()
                log.info("Initial update successful.")
            except Exception as e:
                self._updater.failure_count += 1
                self._updater.last_error_time = time.time()
                self._updater.last_error_message = str(e)
                log.warning(f"Initial update failed. Error: {type(e).__name__} - {e}")
            finally:
                # --- Step 2: Signal the main thread that the initial run is done ---
                log.info("Initial run complete. Unblocking main thread.")
                self._initial_run_event.set()

            # --- Step 3: Start the periodic background loop for all future updates ---
            log.info("Starting the periodic background update loop.")
            self._updater.start_task(self._loop)
            self._loop.run_forever()

        finally:
            if self._loop and self._loop.is_running():
                self._loop.close()
            log.info("Background asyncio event loop closed.")

    def start(self):
        """
        Starts the background thread and BLOCKS until the first
        cookie retrieval attempt is complete.
        """
        if self._thread is not None and self._thread.is_alive():
            log.warning("Updater thread is already running.")
            return

        log.info("Starting background updater thread...")
        # Clear the event flag for this run
        self._initial_run_event.clear()

        self._thread = threading.Thread(target=self._run_background_loop, name="SakaniUpdaterThread", daemon=True)
        self._thread.start()

        # New: Wait here until the background thread signals that the first run is done
        log.info("Main thread is waiting for the initial update to complete...")
        self._initial_run_event.wait()
        log.info("Updater is now running in the background.")

    def stop(self):
        """Stops the updater and the background thread gracefully."""
        if self._thread is None or not self._thread.is_alive() or self._loop is None:
            log.warning("Updater thread is not running.")
            return

        log.info("Requesting updater shutdown...")
        future = asyncio.run_coroutine_threadsafe(self._updater.stop_task(), self._loop)
        try:
            future.result(timeout=5)  # Wait for async stop to finish
        except Exception as e:
            log.error(f"Error while waiting for async stop task: {e}")

        if self._loop.is_running():
            self._loop.call_soon_threadsafe(self._loop.stop)
        
        self._thread.join()
        log.info("Background thread joined. Shutdown complete.")

    # --- Thread-Safe Property Access (Unchanged) ---
    @property
    def status(self) -> Status:
        return self._updater.status

    @property
    def latest_cookies(self) -> Optional[List[Dict[str, Any]]]:
        return self._updater.latest_cookies

    @property
    def cookie_header(self) -> Dict[str, str]:
        return self._updater.cookie_header

    @property
    def success_count(self) -> int:
        return self._updater.success_count

    @property
    def failure_count(self) -> int:
        return self._updater.failure_count