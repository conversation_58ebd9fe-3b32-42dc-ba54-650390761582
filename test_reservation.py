#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Standalone test script for Sakani reservation system
This script allows testing the complete reservation flow step by step
"""

import sys
import os
import json
import subprocess
import hashlib
import uuid
import time
import http.client
import gzip
from urllib.parse import urlparse
from typing import Optional
from sakani_updater import SakaniUpdaterThread

# Unified User Agent for all requests
UNIFIED_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

# Start the SakaniUpdaterThread
updater = SakaniUpdaterThread()
updater.start()

class HttpClientResponseWrapper:
    """A wrapper to make http.client response behave like requests.Response."""

    def __init__(self, status: int, body: bytes, headers: http.client.HTTPMessage):
        self.status_code = status
        self._body = body
        self.headers = headers
        self.text = None
        try:
            self.text = body.decode('utf-8')
        except UnicodeDecodeError:
            pass

    def json(self):
        """Parses the response body as JSON."""
        if self.text is None:
            raise json.JSONDecodeError(
                "Cannot decode JSON from an empty or non-UTF-8 body.", "", 0)
        return json.loads(self.text)


def solve_captcha_with_exe(captcha_url: str, user_agent: str) -> str | None:
    """
    Runs the solver.exe to get a reCAPTCHA token.
    """
    exe_path = "solver_cli.exe" 
    
    if not os.path.exists(exe_path):
        print(f"Error: Executable not found at '{exe_path}'")
        return None

    command = [exe_path, captcha_url, user_agent]
    print(f"Running command: {' '.join(command)}")

    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True
        )

        token = result.stdout.strip()
        token = token.split('**********\n')[1]
        print("Successfully captured token!")
        return token
    except Exception as e:
        print(f"Error solving captcha: {e}")
        return None


class LoginObject:
    def __init__(self, auth: str) -> None:
        headers = {
            'User-Agent': UNIFIED_USER_AGENT,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Sec-GPC': '1',
            'Connection': 'keep-alive',
            'authentication': auth.replace(" ", ""),
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Priority': 'u=0, i',
            'TE': 'trailers',
        }
        
        url = "https://sakani.sa/mainIntermediaryApi/v4/beneficiary/me?include=beneficiary_assets_detail,beneficiary_application,active_booking,active_subsidy_contract"
        parsed_url = urlparse(url)
        host = parsed_url.netloc
        path = parsed_url.path + "?" + parsed_url.query

        conn = http.client.HTTPSConnection(host)
        try:
            conn.request("GET", path, headers=headers)
            res = conn.getresponse()
            body = res.read()
            if res.getheader('Content-Encoding') == 'gzip':
                body = gzip.decompress(body)
            self.response = HttpClientResponseWrapper(res.status, body, res.getheaders())
        finally:
            conn.close()

        if self.isAuthorized:
            jsondata = self.response.json()['data']['attributes']
            self.id = jsondata['id']
            self.name = jsondata['name']
            self.phone_number = jsondata['phone_number']
            self.email_address = jsondata['email_address']
            self.active = jsondata['active']
            self.first_name = jsondata['first_name']
            self.decrypted_national_id_number = jsondata['decrypted_national_id_number']
            self.last_login_date = jsondata['last_login_date']
            self.national_id_number = jsondata['national_id_number']

    @property
    def isAuthorized(self) -> bool:
        return self.response.status_code == 200


class StandaloneSakaniClient:
    """Standalone Sakani client for testing"""

    def __init__(self, auth_token: str, test_mode: bool = False):
        self.auth_token = auth_token
        self.timeout = 5
        self.test_mode = test_mode

        # Real data from actual Sakani projects (from حـجز كابتشا.txt)
        self.real_project_ids = [164, 1493]  # Known active projects
        self.recaptcha_site_key = "6LfcCh8pAAAAAN5MgtopKxl-4Gon3-MKg2RPqmXI"

        if test_mode:
            # In test mode, skip authentication and use dummy data for national_id only
            print("🧪 Running in TEST MODE - skipping authentication")
            self.national_id = "1234567890"  # Dummy for testing
            self.login_obj = None
        else:
            # Get user data
            self.login_obj = LoginObject(auth_token)
            if not self.login_obj.isAuthorized:
                print(f"❌ Authentication failed with status code: {self.login_obj.response.status_code}")
                print(f"❌ Response body: {self.login_obj.response.text[:500] if self.login_obj.response.text else 'No response body'}")
                raise Exception(f"Authentication failed with status code: {self.login_obj.response.status_code}")

            self.national_id = self.login_obj.national_id_number
            print(f"🔧 Authenticated as: {self.login_obj.name} (ID: {self.national_id})")

        # Persistent headers, similar to requests.Session
        self.headers = {
            'User-Agent': UNIFIED_USER_AGENT,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Sec-GPC': '1',
            'Connection': 'keep-alive',
            'authentication': auth_token.replace(" ", ""),
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Priority': 'u=0, i',
            'TE': 'trailers',
        }

    def _make_request(self, method: str, url: str, extra_headers: dict = None, json_payload: dict = None) -> HttpClientResponseWrapper:
        parsed_url = urlparse(url)
        host = parsed_url.netloc
        path = parsed_url.path
        if parsed_url.query:
            path += "?" + parsed_url.query
        
        # Start with persistent headers and update with request-specific headers
        final_headers = self.headers.copy()
        if extra_headers:
            final_headers.update(extra_headers)
        final_headers.update(updater.cookie_header)
        
        conn = http.client.HTTPSConnection(host, timeout=self.timeout)

        body = None
        if json_payload is not None:
            body = json.dumps(json_payload).encode('utf-8')
            final_headers['Content-Type'] = 'application/json'
            final_headers['Content-Length'] = str(len(body))

        try:
            conn.request(method, path, body=body, headers=final_headers)
            res = conn.getresponse()
            response_body = res.read()

            if res.getheader('Content-Encoding') == 'gzip':
                response_body = gzip.decompress(response_body)

            return HttpClientResponseWrapper(res.status, response_body, res.getheaders())
        finally:
            conn.close()

    def generate_recaptcha_token(self, action: str, unit_code: str, project_id: int) -> str | None:
        """
        Generates a recaptcha token using the external solver.
        For testing, we'll try both the raw reCAPTCHA token and a mock JWT.
        """
        user_agent = UNIFIED_USER_AGENT
        captcha_url = f"https://www.google.com/recaptcha/api2/anchor?ar=1&k={self.recaptcha_site_key}&co=aHR0cHM6Ly9zYWthbmkuc2E6NDQz&hl=ar&v=Lu6n5xwy2ghvnPNo3IxkhcCb&size=invisible&anchor-ms=20000&execute-ms=15000&cb=7zfyjqjemyzq"

        recaptcha_challenge = solve_captcha_with_exe(captcha_url, user_agent)
        if not recaptcha_challenge:
            print("Failed to solve captcha")
            return None

        print(f"✅ Generated raw recaptcha token for action: {action}")
        return recaptcha_challenge

    def get_project_data(self, project_id: int) -> dict | None:
        """Get project information"""
        url = f"https://sakani.sa/mainIntermediaryApi/v4/projects/{project_id}?include=amenities,projects_amenities,developer,project_unit_types"
        try:
            response = self._make_request('GET', url, extra_headers=self.headers)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to get project data: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ Error getting project data: {e}")
            return None

    def get_available_units(self, project_id: int) -> list | None:
        """Get available units for a project (like mainclass does)"""
        url = f'https://sakani.sa/marketplaceApi/search/v1/projects/{project_id}/available-units'
        try:
            response = self._make_request('GET', url, extra_headers=self.headers)
            if response.status_code == 200:
                response_json = response.json()
                project_data = self.get_project_data(project_id)

                if 'data' in response_json and len(response_json['data']) > 0:
                    print(f"✅ Found {len(response_json['data'])} available units in project {project_id}")
                    if project_data:
                        project_info = project_data['data']['attributes']
                        print(f"📋 Project: {project_info.get('media_name', 'Unknown')} (Code: {project_info.get('code', 'Unknown')})")

                    # Return the units data
                    return response_json['data']
                else:
                    print(f"❌ No available units found in project {project_id}")
                    return None
            else:
                print(f"❌ Failed to get available units: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ Error getting available units: {e}")
            return None

    def find_available_unit(self) -> tuple[str, int] | None:
        """Find an available unit from known projects"""
        print("🔍 Searching for available units in known projects...")

        for project_id in self.real_project_ids:
            print(f"🔍 Checking project {project_id}...")
            units = self.get_available_units(project_id)

            if units and len(units) > 0:
                # Get the first available unit
                unit = units[0]
                unit_code = unit['attributes']['unit_code']
                unit_id = unit['id']

                print(f"✅ Found available unit!")
                print(f"   Unit ID: {unit_id}")
                print(f"   Unit Code: {unit_code[:20]}...")
                print(f"   Land Number: {unit['attributes'].get('land_number', 'N/A')}")
                print(f"   Price: {unit['attributes'].get('price', 'N/A')} SAR")
                print(f"   Size: {unit['attributes'].get('unit_size', 'N/A')} sqm")

                return unit_code, project_id

        print("❌ No available units found in any known projects")
        return None

    def start_booking_precondition(self, project_id: int) -> dict | None:
        """
        Starts the booking precondition check and generates token.
        """
        payload = {
            "project_id": project_id,
            "source_channel": "web"
        }

        start_booking_headers = {
            'Accept': 'application/json',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'App-Locale': 'ar',
            'Content-Type': 'application/json',
            'Origin': 'https://sakani.sa',
            'Referer': f'https://sakani.sa/app/units/{project_id}',
            'Sec-Ch-Ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': UNIFIED_USER_AGENT,
            'X-Requested-With': 'XMLHttpRequest'
        }

        try:
            response = self._make_request(
                'POST',
                f'https://sakani.sa/mainIntermediaryApi/v4/bookings/offplans/{project_id}/start_booking',
                extra_headers=start_booking_headers,
                json_payload=payload
            )

            if response.status_code == 200:
                response_data = response.json()
                print(f"Successfully started booking precondition check: {response_data}")
                return response_data
            else:
                print(f"Start booking failed with status code: {response.status_code}")
                print(f"Response: {response.text}")
                if response.status_code == 403:
                    print("🔍 403 error suggests authentication issue or missing cookies")
                return None

        except Exception as e:
            print(f"Error during start booking: {e}")
            return None

    def validate_request(self, unit_code: str, project_id: int) -> dict | None:
        """
        Validates the request using captcha and returns recaptcha_auth_token.
        Based on testing.py validate_request function.
        """
        headers = {
            'accept': 'application/json',
            'accept-language': 'ar',
            'app-locale': 'ar',
            'content-type': 'application/json',
            'origin': 'https://sakani.sa',
            'priority': 'u=1, i',
            'referer': 'https://sakani.sa/app/units/1042379',
            'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        }
        # Solve captcha
        captcha_url = f"https://www.google.com/recaptcha/api2/anchor?ar=1&k={self.recaptcha_site_key}&co=aHR0cHM6Ly9zYWthbmkuc2E6NDQz&hl=ar&v=Lu6n5xwy2ghvnPNo3IxkhcCb&size=invisible&anchor-ms=20000&execute-ms=15000&cb=7zfyjqjemyzq"
        recaptcha_challenge = solve_captcha_with_exe(captcha_url, UNIFIED_USER_AGENT)

        if not recaptcha_challenge:
            print("❌ Failed to solve captcha for validate_request")
            return None

        json_data = {
            'data': {
                'attributes': {
                    'action': 'start_booking',
                    'identifier': self.national_id,
                    'recaptcha_challenge': recaptcha_challenge,
                    'recaptcha_type': 'grecaptchaV3',
                    'payload': {
                        'data': {
                            'attributes': {
                                'unit_code': unit_code,
                                'project_id': project_id,
                            },
                        },
                    },
                    'site_key': self.recaptcha_site_key,
                },
            },
        }
        try:
            import http.client
            conn = http.client.HTTPSConnection('sakani.sa')
            conn.request("POST", "/captchaApi/grecaptcha/validate", body=json.dumps(json_data).encode('utf-8'), headers=headers)
            response = conn.getresponse()
            conn.close()
            response_data = response.read()
            if response.getheader('Content-Encoding') == 'gzip':
                response_data = gzip.decompress(response_data)
            response = HttpClientResponseWrapper(response.status, response_data, response.getheaders())
            ok = response.status_code == 200
            if not ok:
                raise Exception(f"Request failed with status code: {response.status_code}")
            # response = self._make_request(
            #     'POST',
            #     'https://sakani.sa/captchaApi/grecaptcha/validate',
            #     extra_headers=headers,
            #     json_payload=json_data
            # )

            if response.status_code == 200:
                response_data = response.json()
                print(f"✅ Request validation successful: {response_data}")
                return response_data
            else:
                print(f"❌ Request validation failed: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            print(f"❌ Error during request validation: {e}")
            return None

    def start_booking_cqrs_check(self, request_id: str, project_id: int) -> dict | None:
        """
        Checks the start booking CQRS status with retry logic.
        Based on testing.py start_booking_cqrs_check function.
        Keeps retrying until success using the exact logic from testing.py.
        """
        headers = {
            'accept': 'application/json',
            'accept-language': 'ar',
            'app-locale': 'ar',
            'content-type': 'application/json',
            'priority': 'u=1, i',
            'referer': f'https://sakani.sa/app/units/{project_id}',
            'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': UNIFIED_USER_AGENT,
        }

        try:
            max_attempts = 30  # Increased attempts for better reliability
            attempts = 0

            print(f"🔄 Starting CQRS check for request_id: {request_id}")

            while attempts < max_attempts:
                # Build URL with query parameters
                url = f'https://sakani.sa/sakani-queries-service/cqrs-res?topic=booking_precondition_and_generate_token_check_completed&request_id={request_id}'

                response = self._make_request(
                    'GET',
                    url,
                    extra_headers=headers
                )

                if response.status_code == 200:
                    response_data = response.json()

                    # Use the exact logic from testing.py
                    if not response_data.get(list(response_data.keys())[0], None) == "success":
                        print(f"Waiting... attempt {attempts + 1}/{max_attempts}")
                        time.sleep(1.0)  # Slightly longer wait
                        attempts += 1
                        continue
                    else:
                        print(f"✅ Start booking CQRS check successful after {attempts + 1} attempts!")
                        print(f"📄 Response: {response_data}")
                        return response_data
                else:
                    print(f"❌ Start booking CQRS check failed with status code: {response.status_code}")
                    print(f"❌ Response: {response.text}")
                    return None

            print(f"❌ Maximum attempts ({max_attempts}) reached for start booking CQRS check")
            return None

        except Exception as e:
            print(f"❌ Error during start booking CQRS check: {e}")
            return None

    def check_booking_precondition_status(self, request_id: str, project_id: int) -> dict | None:
        """
        Checks the booking precondition status using CQRS.
        """
        cqrs_headers = {
            'Accept': 'application/json',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'App-Locale': 'ar',
            'Referer': f'https://sakani.sa/app/units/{project_id}',
            'Sec-Ch-Ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': UNIFIED_USER_AGENT,
            'X-Requested-With': 'XMLHttpRequest'
        }

        try:
            max_attempts = 10
            attempts = 0

            while attempts < max_attempts:
                response = self._make_request(
                    'GET',
                    f'https://sakani.sa/sakani-queries-service/cqrs-res?topic=booking_precondition_and_generate_token_check_completed&request_id={request_id}',
                    extra_headers=cqrs_headers
                )

                if response.status_code == 200:
                    response_data = response.json()

                    # Check if still waiting
                    if 'waiting' in str(response_data):
                        print(f"Still waiting... attempt {attempts + 1}")
                        time.sleep(0.5)
                        attempts += 1
                        continue

                    return response_data
                else:
                    print(f"CQRS check failed with status code: {response.status_code}")
                    return None

            print("Maximum attempts reached for CQRS check")
            return None

        except Exception as e:
            print(f"Error during CQRS status check: {e}")
            return None

    def reserve_unit(self, unit_code: str, recaptcha_auth_token: str) -> dict | None:
        """
        Reserves a unit using the correct API structure from HAR analysis.
        Uses the recaptcha_auth_token from validate_request (not direct recaptcha token).
        """
        # Structure based on HAR file analysis
        payload = {
            "data": {
                "id": "",
                "type": "units",
                "attributes": {
                    "unit_code": unit_code,
                    "national_id_number": self.national_id
                }
            },
            "recaptcha_auth_token": recaptcha_auth_token
        }

        # Headers based on HAR analysis
        reserve_headers = {
            'Accept': 'application/json',
            'Accept-Language': 'ar',
            'App-Locale': 'ar',
            'Content-Type': 'application/json',
            'Origin': 'https://sakani.sa',
            'Referer': 'https://sakani.sa/app/units/1042379',
            'Sec-Ch-Ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': UNIFIED_USER_AGENT,
            'Priority': 'u=1, i'
        }

        try:
            response = self._make_request(
                "POST",
                "https://sakani.sa/mainIntermediaryApi/v4/units/reserve",
                json_payload=payload,
                extra_headers=reserve_headers
            )

            if response.status_code in [200, 202]:  # 202 is the actual success status from HAR
                print(f"✅ Unit reservation successful: {response.status_code}")
                return response.json()
            else:
                print(f"❌ Unit reservation failed: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            print(f"❌ Error during unit reservation: {e}")
            return None


class ReservationTester:
    """Test class for Sakani reservation system"""

    def __init__(self, auth_token: str, test_mode: bool = False):
        self.auth_token = auth_token
        self.test_mode = test_mode
        self.client = StandaloneSakaniClient(auth_token, test_mode)
        print(f"🔧 Initialized standalone tester with National ID: {self.client.national_id}")
    
    def test_recaptcha_generation(self, unit_code: str, project_id: int) -> Optional[str]:
        """Test the recaptcha token generation step"""
        print("\n🔐 Testing Recaptcha Token Generation...")
        print("-" * 50)

        # Based on HAR analysis, use "start_booking" action
        action = "start_booking"

        print(f"🔍 Generating recaptcha token for action: '{action}'")
        try:
            recaptcha_token = self.client.generate_recaptcha_token(action, unit_code, project_id)

            if recaptcha_token:
                print(f"✅ Recaptcha token generation successful!")
                print(f"🎫 Recaptcha token: {recaptcha_token[:50]}...")
                return recaptcha_token
            else:
                print(f"❌ Recaptcha token generation failed")
                return None

        except Exception as e:
            print(f"❌ Error during recaptcha token generation: {e}")
            return None
    
    def test_booking_precondition(self, project_id: int) -> Optional[dict]:
        """Test the booking precondition step"""
        print("\n📋 Testing Booking Precondition...")
        print("-" * 50)
        
        try:
            response = self.client.start_booking_precondition(project_id)
            
            if response:
                print("✅ Booking precondition started successfully!")
                print(f"📄 Response: {json.dumps(response, indent=2)}")
                return response
            else:
                print("❌ Booking precondition failed!")
                return None
                
        except Exception as e:
            print(f"❌ Error during booking precondition: {e}")
            return None
    
    def test_validate_request(self, unit_code: str, project_id: int) -> Optional[dict]:
        """Test the validate request step"""
        print("\n🔐 Testing Request Validation...")
        print("-" * 50)

        try:
            response = self.client.validate_request(unit_code, project_id)

            if response:
                print("✅ Request validation successful!")
                print(f"📄 Response: {json.dumps(response, indent=2)}")

                # Check for recaptcha_auth_token
                recaptcha_auth_token = response.get('data', {}).get('attributes', {}).get('recaptcha_auth_token')
                if recaptcha_auth_token:
                    print(f"🎫 Recaptcha auth token obtained: {recaptcha_auth_token[:50]}...")
                else:
                    print("⚠️  No recaptcha auth token found in response")

                return response
            else:
                print("❌ Request validation failed!")
                return None

        except Exception as e:
            print(f"❌ Error during request validation: {e}")
            return None

    def test_start_booking_cqrs_check(self, request_id: str, project_id: int) -> Optional[dict]:
        """Test the start booking CQRS check with retry logic"""
        print("\n🔍 Testing Start Booking CQRS Check (with retry logic)...")
        print("-" * 50)

        try:
            print(f"🔄 Will keep retrying CQRS check until success...")
            response = self.client.start_booking_cqrs_check(request_id, project_id)

            if response:
                print("✅ Start booking CQRS check completed successfully!")
                print(f"📄 Final Response: {json.dumps(response, indent=2)}")
                return response
            else:
                print("❌ Start booking CQRS check failed after all retries!")
                return None

        except Exception as e:
            print(f"❌ Error during start booking CQRS check: {e}")
            return None

    def test_precondition_status(self, request_id: str, project_id: int) -> Optional[dict]:
        """Test the precondition status check"""
        print("\n🔍 Testing Precondition Status Check...")
        print("-" * 50)

        try:
            response = self.client.check_booking_precondition_status(request_id, project_id)

            if response:
                print("✅ Precondition status check successful!")
                print(f"📄 Response: {json.dumps(response, indent=2)}")

                # Check for blocking reasons
                block_reasons = response.get('data', {}).get('block_booking_reason', [])
                if block_reasons:
                    print(f"⚠️  Booking blocked due to: {block_reasons}")
                else:
                    print("✅ No blocking reasons found!")

                # Check for booking token
                booking_token = response.get('data', {}).get('booking_session', {}).get('data', {}).get('attributes', {}).get('booking_token')
                if booking_token:
                    print(f"🎫 Booking token obtained: {booking_token[:50]}...")
                else:
                    print("⚠️  No booking token found in response")

                return response
            else:
                print("❌ Precondition status check failed!")
                return None

        except Exception as e:
            print(f"❌ Error during precondition status check: {e}")
            return None
    
    def test_unit_reservation(self, unit_code: str, recaptcha_auth_token: str) -> bool:
        """Test the unit reservation step using recaptcha_auth_token from validate_request"""
        print("\n🏠 Testing Unit Reservation...")
        print("-" * 50)

        try:
            print(f"📤 Sending reservation request for unit: {unit_code}")
            print(f"🎫 Using recaptcha_auth_token: {recaptcha_auth_token[:50]}...")
            response = self.client.reserve_unit(unit_code, recaptcha_auth_token)

            if response:
                print("✅ Unit reservation request successful!")
                print(f"📄 Response: {json.dumps(response, indent=2)}")
                return True
            else:
                print("❌ Unit reservation failed!")
                return False

        except Exception as e:
            print(f"❌ Error during unit reservation: {e}")
            return False
    
    def run_complete_test(self, unit_code: str, project_id: int) -> bool:
        """Run the complete reservation test flow based on HAR analysis"""
        print("🚀 Starting Complete Reservation Test (Updated API Flow)")
        print("=" * 60)
        print(f"Unit Code: {unit_code}")
        print(f"Project ID: {project_id}")
        print(f"National ID: {self.client.national_id}")
        print("=" * 60)

        # Step 1: Generate recaptcha token (based on HAR analysis)
        recaptcha_token = self.test_recaptcha_generation(unit_code, project_id)
        if not recaptcha_token:
            print("\n❌ Test failed at recaptcha token generation step")
            return False

        # Step 2: Test unit reservation (direct API call with recaptcha token)
        print("\n🎯 Testing direct unit reservation (main API from HAR)...")
        reservation_success = self.test_unit_reservation(unit_code, recaptcha_token)

        if reservation_success:
            print("\n🎉 Complete reservation test PASSED!")
            return True
        else:
            print("\n❌ Test failed at unit reservation step")
            return False

    def run_complete_test_with_new_flow(self, unit_code: str, project_id: int) -> bool:
        """
        Run the complete test with the new flow from testing.py
        MANDATORY FLOW: validate_request -> start_booking -> start_booking_cqrs_check -> reserve
        The validate_request step is REQUIRED to get recaptcha_auth_token for reservation.
        """
        print("🚀 Starting Complete Reservation Test with New Flow (testing.py style)")
        print("=" * 60)
        print(f"Unit Code: {unit_code}")
        print(f"Project ID: {project_id}")
        print(f"National ID: {self.client.national_id}")
        print("⚠️  MANDATORY: validate_request must be called first to get recaptcha_auth_token")
        print("=" * 60)

        # Step 1: Validate request (MANDATORY - get recaptcha_auth_token)
        print("\n🔐 Step 1: MANDATORY validate_request to get recaptcha_auth_token...")
        validate_response = self.test_validate_request(unit_code, project_id)
        if not validate_response:
            print("\n❌ Test failed at MANDATORY validate request step")
            return False

        recaptcha_auth_token = validate_response.get('data', {}).get('attributes', {}).get('recaptcha_auth_token')
        if not recaptcha_auth_token:
            print("\n❌ No recaptcha_auth_token found in validate response - CANNOT PROCEED")
            return False

        print(f"✅ Got MANDATORY recaptcha_auth_token: {recaptcha_auth_token[:50]}...")

        # Step 2: Start booking precondition
        print("\n📋 Step 2: Start booking precondition to get request_id...")
        precondition_response = self.test_booking_precondition(project_id)
        if not precondition_response:
            print("\n❌ Test failed at booking precondition step")
            return False

        request_id = precondition_response.get('data', {}).get('request_id')
        if not request_id:
            print("\n❌ No request_id found in precondition response")
            return False

        print(f"✅ Got start_booking request_id: {request_id}")

        # Step 3: Keep retrying start booking CQRS check until success
        print("\n🔄 Step 3: Start booking CQRS check with retry logic until success...")
        cqrs_response = self.test_start_booking_cqrs_check(request_id, project_id)
        if not cqrs_response:
            print("\n❌ Test failed at start booking CQRS check step")
            return False

        print("✅ Start booking CQRS check completed successfully!")

        # Step 4: Test unit reservation with recaptcha_auth_token (from Step 1)
        print(f"\n🏠 Step 4: Unit reservation using recaptcha_auth_token from validate_request...")
        reservation_success = self.test_unit_reservation(unit_code, recaptcha_auth_token)

        if reservation_success:
            print("\n🎉 Complete reservation test with new flow PASSED!")
            return True
        else:
            print("\n❌ Test failed at unit reservation step")
            return False

    def run_complete_test_with_preconditions(self, unit_code: str, project_id: int) -> bool:
        """Run the complete test with preconditions (requires real authentication)"""
        print("🚀 Starting Complete Reservation Test with Preconditions")
        print("=" * 60)
        print(f"Unit Code: {unit_code}")
        print(f"Project ID: {project_id}")
        print(f"National ID: {self.client.national_id}")
        print("=" * 60)

        # Step 1: Test booking precondition
        precondition_response = self.test_booking_precondition(project_id)
        if not precondition_response:
            print("\n❌ Test failed at booking precondition step")
            return False

        request_id = precondition_response.get('data', {}).get('request_id')
        if not request_id:
            print("\n❌ No request_id found in precondition response")
            return False

        # Step 2: Test precondition status
        status_response = self.test_precondition_status(request_id, project_id)
        if not status_response:
            print("\n❌ Test failed at precondition status step")
            return False

        # Check if booking is blocked
        block_reasons = status_response.get('data', {}).get('block_booking_reason', [])
        if block_reasons:
            print(f"\n⚠️  Cannot proceed with reservation due to blocking reasons: {block_reasons}")
            return False

        # Step 3: Generate recaptcha token (based on HAR analysis)
        recaptcha_token = self.test_recaptcha_generation(unit_code, project_id)
        if not recaptcha_token:
            print("\n❌ Test failed at recaptcha token generation step")
            return False

        # Step 4: Test unit reservation (direct API call with recaptcha token)
        reservation_success = self.test_unit_reservation(unit_code, recaptcha_token)

        if reservation_success:
            print("\n🎉 Complete reservation test PASSED!")
            return True
        else:
            print("\n❌ Test failed at unit reservation step")
            return False


def test_individual_components():
    """Test individual components that don't require authentication"""
    print("🧪 Testing Individual Components")
    print("=" * 40)

    # Test 1: Test captcha solver
    print("\n🔐 Testing Captcha Solver...")
    captcha_url = "https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LfcCh8pAAAAAN5MgtopKxl-4Gon3-MKg2RPqmXI&co=aHR0cHM6Ly9zYWthbmkuc2E6NDQz&hl=ar&v=Lu6n5xwy2ghvnPNo3IxkhcCb&size=invisible&anchor-ms=20000&execute-ms=15000&cb=7zfyjqjemyzq"
    user_agent = UNIFIED_USER_AGENT

    try:
        token = solve_captcha_with_exe(captcha_url, user_agent)
        if token:
            print(f"✅ Captcha solver working! Token: {token[:50]}...")
        else:
            print("❌ Captcha solver failed")
    except Exception as e:
        print(f"❌ Error testing captcha solver: {e}")

    # Test 2: Test SakaniUpdaterThread
    print("\n🔄 Testing SakaniUpdaterThread...")
    try:
        print(f"✅ SakaniUpdaterThread is working (already started)")
        print(f"📊 Cookie count: {len(updater.latest_cookies) if updater.latest_cookies else 0}")
        print(f"📊 Success count: {updater.success_count}")
        print(f"📊 Failure count: {updater.failure_count}")
    except Exception as e:
        print(f"❌ Error with SakaniUpdaterThread: {e}")

    print("\n🎯 Component tests completed!")


def main():
    """Main function to run the test"""
    print("🧪 Standalone Sakani Reservation Tester")
    print("=" * 40)

    # First test individual components
    test_individual_components()

    print("\n" + "=" * 40)
    print("🚀 Full Integration Test with REAL DATA")
    print("=" * 40)

    # Use test mode to bypass authentication but get real unit data
    auth_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************.R0ATy8phAUJBY17Fin5Rj_AAsmIOgnI6gIQ9NGDT5TLbM1ZdWTll_yFCEjBt1tu00igbgHlQVNy9TqPEId2g7vPfCbCVDeuKAJlFWnw-dudBH2bdxmphJ8soa75MeT3qzCoNeGgvQmvDFClwmG2EI3iRUzv3qKN1rc0kjISwQYvZr11A-q7twnhifrb3LSvmZcu4y8Ir4wvvSNukGYfAseom89sdnBKzxXZBukINxIdafyYjRw_kUZSA4zk_y-rk8etakquHE8RxBgmFExs26bLywMCdMsBE9JdWDHG8rHjUaGk9XzIcvy4sMpAM58KgnEASonCYDXhxi6mppnCoFg"  # Replace with real token when needed
    test_mode = False

    # Create tester and run test
    try:
        tester = ReservationTester(auth_token, test_mode)

        # Find real available unit
        print("🔍 Finding real available unit...")
        unit_data = tester.client.find_available_unit()

        if not unit_data:
            print("❌ No available units found. Cannot proceed with integration test.")
            print("💡 Try running with real authentication token to access more projects.")
            return

        unit_code, project_id = unit_data

        print(f"✅ Using REAL unit data:")
        print(f"   Unit code: {unit_code[:20]}...")
        print(f"   Project ID: {project_id}")
        print("-" * 40)

        # When test_mode=False, only run the full flow with recaptcha_auth_token
        if test_mode:
            # Test mode: run both flows for comparison
            print("\n🧪 Testing Original Flow...")
            success_original = tester.run_complete_test(unit_code, project_id)

            print("\n🧪 Testing New Flow (with validate_request and start_booking_cqrs_check)...")
            success_new = tester.run_complete_test_with_new_flow(unit_code, project_id)

            if success_original or success_new:
                print("\n✅ At least one test flow completed successfully!")
                if success_original:
                    print("   ✅ Original flow: PASSED")
                else:
                    print("   ❌ Original flow: FAILED")
                if success_new:
                    print("   ✅ New flow: PASSED")
                else:
                    print("   ❌ New flow: FAILED")
            else:
                print("\n❌ Both test flows failed. Check the output above for details.")
        else:
            # Production mode: only run the full flow with recaptcha_auth_token
            print("\n🚀 Running FULL FLOW with recaptcha_auth_token (validate_request mandatory)...")
            success = tester.run_complete_test_with_new_flow(unit_code, project_id)

            if success:
                print("\n✅ Full flow with recaptcha_auth_token completed successfully!")
            else:
                print("\n❌ Full flow failed. Check the output above for details.")
    except Exception as e:
        print(f"❌ Failed to initialize tester: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()





